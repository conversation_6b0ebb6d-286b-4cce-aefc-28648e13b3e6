"""
HSV颜色处理器 - 基于HSV颜色空间的激光点检测和分析系统

功能描述:
- 使用HSV颜色空间进行颜色阈值处理
- 支持激光点检测和颜色分类 (红色/绿色激光)
- 提供多种图像处理结果显示 (二值化、边缘、轮廓、矩形框)
- 支持ROI区域选择和参数文件配置
- 实时摄像头图像处理和多窗口显示

作者: [作者名]
创建时间: [创建时间]
"""

import cv2
import numpy as np
import os

# ==================== 全局参数配置 ====================
# HSV颜色阈值范围 - HSV颜色空间参数
H_MIN = 80      # 色调(Hue)最小值 (0-179)
H_MAX = 177     # 色调(Hue)最大值 (0-179)
S_MIN = 59      # 饱和度(Saturation)最小值 (0-255)
S_MAX = 255     # 饱和度(Saturation)最大值 (0-255)
V_MIN = 7       # 明度(Value)最小值 (0-255)
V_MAX = 255     # 明度(Value)最大值 (0-255)

# 摄像头设置
EXPOSURE = -7   # 摄像头曝光度设置

# ROI (Region of Interest) 感兴趣区域设置
ROI = [763,163,1080,460]  # 格式: [x1, y1, x2, y2] 左上角和右下角坐标

# 图像处理选项
INVERT = False  # 是否反转二值化结果

# 参数加载控制
USE_THRESHOLD_FILE = True  # 是否从配置文件加载参数

# 形态学处理参数 - 膨胀操作用于连接断开的区域
DILATE_KERNEL_SIZE = 2  # 膨胀操作的核大小
DILATE_ITERATIONS = 1   # 膨胀操作的迭代次数

# ==================== 参数加载函数 ====================
def load_parameters():
    """
    从配置文件加载HSV处理参数

    文件格式 (thresholds_HSV.txt):
    第1行: HSV下限值，格式: h_min,s_min,v_min
    第2行: HSV上限值，格式: h_max,s_max,v_max
    第3行: ROI区域，格式: x1,y1,x2,y2
    第4行: 曝光度值 (可选)

    Returns:
        None - 直接修改全局变量
    """
    global H_MIN, H_MAX, S_MIN, S_MAX, V_MIN, V_MAX, EXPOSURE, ROI

    # 检查是否启用文件加载
    if not USE_THRESHOLD_FILE:
        print("不读取阈值文件，使用全局参数")
        return

    try:
        # 检查配置文件是否存在
        if os.path.exists("thresholds_HSV.txt"):
            with open("thresholds_HSV.txt", "r") as f:
                lines = f.readlines()

                # 解析HSV下限值 (第1行)
                hsv_lower = [int(x) for x in lines[0].strip().split(",")]
                # 解析HSV上限值 (第2行)
                hsv_upper = [int(x) for x in lines[1].strip().split(",")]

                # 设置HSV阈值参数
                H_MIN = hsv_lower[0]  # 色调下限
                S_MIN = hsv_lower[1]  # 饱和度下限
                V_MIN = hsv_lower[2]  # 明度下限

                H_MAX = hsv_upper[0]  # 色调上限
                S_MAX = hsv_upper[1]  # 饱和度上限
                V_MAX = hsv_upper[2]  # 明度上限

                # 解析ROI区域 (第3行)
                ROI = [int(x) for x in lines[2].strip().split(",")]

                # 解析曝光值 (第4行，可选)
                if len(lines) > 3:
                    EXPOSURE = int(lines[3].strip())

                # 输出加载成功信息
                print(f"已加载HSV参数: H({H_MIN}-{H_MAX}), S({S_MIN}-{S_MAX}), V({V_MIN}-{V_MAX})")
                print(f"ROI: {ROI}, 曝光值: {EXPOSURE}")
        else:
            print("阈值文件不存在，使用默认参数")
    except Exception as e:
        print(f"加载参数时出错: {str(e)}")
        print("使用默认参数")

# ==================== 激光颜色分析函数 ====================
def analyze_laser_color(image, contour):
    """
    分析轮廓内部区域的颜色，判断激光类型

    通过比较轮廓内部像素的RGB通道值来区分红色激光和绿色激光。
    使用掩码技术只分析轮廓内部的有效像素，提高检测准确性。

    Args:
        image: 输入的BGR格式图像
        contour: OpenCV轮廓对象，包含边界点坐标

    Returns:
        str: 激光类型 - "RED"(红色激光), "GREEN"(绿色激光), "UNKNOWN"(未知类型)
    """
    # 创建轮廓掩码 - 用于提取轮廓内部像素
    mask = np.zeros(image.shape[:2], dtype=np.uint8)
    cv2.drawContours(mask, [contour], 0, 255, -1)  # 填充轮廓内部为白色(255)

    # 使用掩码提取轮廓内的像素值
    masked_image = cv2.bitwise_and(image, image, mask=mask)

    # 验证轮廓内是否有有效像素
    non_zero_pixels = cv2.countNonZero(mask)
    if non_zero_pixels == 0:
        return "UNKNOWN"

    # 计算各颜色通道的像素值总和 (BGR格式)
    b_sum = int(np.sum(masked_image[:, :, 0]))  # 蓝色通道总和
    g_sum = int(np.sum(masked_image[:, :, 1]))  # 绿色通道总和
    r_sum = int(np.sum(masked_image[:, :, 2]))  # 红色通道总和

    # 输出调试信息用于参数调优
    print(f"轮廓内部像素统计 - 红色总和: {r_sum}, 绿色总和: {g_sum}, 蓝色总和: {b_sum}")

    # 激光类型判断逻辑 - 基于红绿通道值比较
    if r_sum > g_sum * 1.0:      # 红色值显著大于绿色值
        return "RED"
    elif g_sum > r_sum * 0.8:    # 绿色值显著大于红色值
        return "GREEN"
    else:
        return "UNKNOWN"         # 无法明确判断的情况

def process_frame(frame):
    """使用HSV阈值处理帧并查找轮廓"""
    # 检查空帧
    if frame is None or frame.size == 0:
        return None, None, None, None, None
    
    # 复制帧以避免修改原始数据
    frame = frame.copy()
    orig_frame = frame.copy()
    roi_frame = None
    
    # 最终处理结果
    binary_result = None        # 二值化结果
    contour_result = None       # 轮廓结果
    edge_result = None          # 边缘检测结果
    rectangle_result = None     # 矩形框结果
    
    # 应用ROI
    if ROI is not None:
        x1, y1, x2, y2 = ROI
        height, width = frame.shape[:2]
        
        # 确保坐标在图像范围内
        x1 = max(0, min(x1, width-1))
        y1 = max(0, min(y1, height-1))
        x2 = max(0, min(x2, width-1))
        y2 = max(0, min(y2, height-1))
        
        # 检查ROI是否有效
        if x2 > x1 and y2 > y1:
            # 提取ROI，不进行缩放操作
            roi_frame = orig_frame[y1:y2, x1:x2].copy()
            
            # 在原始图像上绘制ROI区域边框
            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
            # 对ROI区域进行处理
            if roi_frame.size > 0:
                # 转换为HSV颜色空间
                hsv = cv2.cvtColor(roi_frame, cv2.COLOR_BGR2HSV)
                
                # 设置HSV阈值范围
                lower_bound = np.array([H_MIN, S_MIN, V_MIN])
                upper_bound = np.array([H_MAX, S_MAX, V_MAX])
                
                # 应用阈值
                binary = cv2.inRange(hsv, lower_bound, upper_bound)
                
                # 如果启用反转，则应用反转
                if INVERT:
                    binary = cv2.bitwise_not(binary)
                
                # 对二值化结果进行膨胀
                kernel = np.ones((DILATE_KERNEL_SIZE, DILATE_KERNEL_SIZE), np.uint8)
                binary = cv2.dilate(binary, kernel, iterations=DILATE_ITERATIONS)
                
                # 创建二值化结果的彩色版本
                binary_result = cv2.cvtColor(binary, cv2.COLOR_GRAY2BGR)
                
                # 寻找边缘
                edges = cv2.Canny(binary, 50, 150)
                edge_result = cv2.cvtColor(edges, cv2.COLOR_GRAY2BGR)
                
                # 寻找轮廓
                contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                # 创建轮廓显示图像
                contour_result = roi_frame.copy()
                cv2.drawContours(contour_result, contours, -1, (0, 255, 255), 2)  # 黄色线条
                
                # 创建矩形框显示图像
                rectangle_result = roi_frame.copy()
                
                # 对每个轮廓画出外接矩形
                for contour in contours:
                    # 仅处理有一定面积的轮廓
                    if cv2.contourArea(contour) > 1:
                        # 获取外接矩形
                        x, y, w, h = cv2.boundingRect(contour)
                        
                        # 分析激光颜色 - 只统计轮廓内部的像素
                        laser_type = analyze_laser_color(roi_frame, contour)
                        
                        # 根据激光类型设置矩形颜色
                        rect_color = (0, 255, 255)  # 默认黄色
                        if laser_type == "RED":
                            rect_color = (0, 0, 255)  # 红色激光用红色矩形
                        elif laser_type == "GREEN":
                            rect_color = (0, 255, 0)  # 绿色激光用绿色矩形
                        
                        # 绘制矩形
                        cv2.rectangle(rectangle_result, (x, y), (x+w, y+h), rect_color, 2)
                        
                        # 计算中心点
                        center_x = x + w // 2
                        center_y = y + h // 2
                        # 在矩形中心画一个十字
                        cv2.drawMarker(rectangle_result, (center_x, center_y), (0, 0, 255), 
                                      cv2.MARKER_CROSS, 10, 2)
                        
                        # 显示激光类型文本
                        cv2.putText(rectangle_result, laser_type, (x, y-5), 
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, rect_color, 1)
    else:
        # 无有效ROI时，直接处理整个帧
        # 转换为HSV颜色空间
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        
        # 设置HSV阈值范围
        lower_bound = np.array([H_MIN, S_MIN, V_MIN])
        upper_bound = np.array([H_MAX, S_MAX, V_MAX])
        
        # 应用阈值
        binary = cv2.inRange(hsv, lower_bound, upper_bound)
        
        # 如果启用反转，则应用反转
        if INVERT:
            binary = cv2.bitwise_not(binary)
        
        # 对二值化结果进行膨胀
        kernel = np.ones((DILATE_KERNEL_SIZE, DILATE_KERNEL_SIZE), np.uint8)
        binary = cv2.dilate(binary, kernel, iterations=DILATE_ITERATIONS)
        
        # 创建二值化结果的彩色版本
        binary_result = cv2.cvtColor(binary, cv2.COLOR_GRAY2BGR)
        
        # 寻找边缘
        edges = cv2.Canny(binary, 50, 150)
        edge_result = cv2.cvtColor(edges, cv2.COLOR_GRAY2BGR)
        
        # 寻找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 创建轮廓显示图像
        contour_result = frame.copy()
        cv2.drawContours(contour_result, contours, -1, (0, 255, 255), 2)  # 黄色线条
        
        # 创建矩形框显示图像
        rectangle_result = frame.copy()
        
        # 对每个轮廓画出外接矩形
        for contour in contours:
            # 仅处理有一定面积的轮廓
            if cv2.contourArea(contour) > 1:
                # 获取外接矩形
                x, y, w, h = cv2.boundingRect(contour)
                
                # 分析激光颜色 - 只统计轮廓内部的像素
                laser_type = analyze_laser_color(frame, contour)
                
                # 根据激光类型设置矩形颜色
                rect_color = (0, 255, 255)  # 默认黄色
                if laser_type == "RED":
                    rect_color = (0, 0, 255)  # 红色激光用红色矩形
                elif laser_type == "GREEN":
                    rect_color = (0, 255, 0)  # 绿色激光用绿色矩形
                
                # 绘制矩形
                cv2.rectangle(rectangle_result, (x, y), (x+w, y+h), rect_color, 2)
                
                # 计算中心点
                center_x = x + w // 2
                center_y = y + h // 2
                # 在矩形中心画一个十字
                cv2.drawMarker(rectangle_result, (center_x, center_y), (0, 0, 255), 
                              cv2.MARKER_CROSS, 10, 2)
                
                # 显示激光类型文本
                cv2.putText(rectangle_result, laser_type, (x, y-5), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, rect_color, 1)
    
    return frame, binary_result, edge_result, contour_result, rectangle_result

def main():
    # 添加全局变量声明
    global USE_THRESHOLD_FILE
    
    # 加载参数
    load_parameters()
    
    # 打开摄像头
    cap = cv2.VideoCapture(0)
    
    # 获取摄像头尺寸
    cam_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    cam_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    
    # 计算窗口尺寸，保持视频原始比例
    # 假设设计窗口宽度480，高度根据比例计算
    display_width = 480
    display_height = int(display_width * cam_height / cam_width)
    
    # 设置曝光
    try:
        cap.set(cv2.CAP_PROP_AUTO_EXPOSURE, 0)
        cap.set(cv2.CAP_PROP_EXPOSURE, EXPOSURE)
        print(f"摄像头曝光值设置为 {EXPOSURE}")
    except:
        print("设置曝光值失败，使用默认值")
    
    # 创建自定义大小的窗口
    cv2.namedWindow("Original", cv2.WINDOW_NORMAL)
    cv2.namedWindow("Binary", cv2.WINDOW_NORMAL)
    cv2.namedWindow("Edges", cv2.WINDOW_NORMAL)
    cv2.namedWindow("Contours", cv2.WINDOW_NORMAL)
    cv2.namedWindow("Rectangles", cv2.WINDOW_NORMAL)
    
    # 调整窗口位置和大小
    cv2.moveWindow("Original", 50, 50)
    cv2.moveWindow("Binary", 50, 50 + display_height + 40)
    cv2.moveWindow("Edges", 50 + display_width + 20, 50)
    cv2.moveWindow("Contours", 50 + display_width + 20, 50 + display_height + 40)
    cv2.moveWindow("Rectangles", 50 + 2*(display_width + 20), 50)
    
    # 调整窗口大小
    cv2.resizeWindow("Original", display_width, display_height)
    cv2.resizeWindow("Binary", display_width, display_height)
    cv2.resizeWindow("Edges", display_width, display_height)
    cv2.resizeWindow("Contours", display_width, display_height)
    cv2.resizeWindow("Rectangles", display_width, display_height)
    
    # 构建参数文本，包含控制参数信息
    mode_text = "使用阈值文件" if USE_THRESHOLD_FILE else "使用默认参数"
    dilate_text = f"膨胀: 核大小={DILATE_KERNEL_SIZE}, 迭代={DILATE_ITERATIONS}"
    param_text = f"HSV: H({H_MIN}-{H_MAX}) S({S_MIN}-{S_MAX}) V({V_MIN}-{V_MAX}) Exp:{EXPOSURE} {dilate_text}"
    print(param_text)
    
    while True:
        # 读取帧
        ret, frame = cap.read()
        if not ret:
            print("无法从摄像头读取")
            break
            
        # 处理图像
        original, binary, edges, contours, rectangles = process_frame(frame)
        
        if original is None:
            print("处理帧时出错")
            break
            
        # 添加参数文本到原始图像
        cv2.putText(original, param_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
        
        # 显示所有处理阶段的图像
        cv2.imshow("Original", original)
        
        if binary is not None:
            cv2.imshow("Binary", binary)
            
        if edges is not None:
            cv2.imshow("Edges", edges)
            
        if contours is not None:
            cv2.imshow("Contours", contours)
            
        if rectangles is not None:
            cv2.imshow("Rectangles", rectangles)
        
        # 等待按键
        key = cv2.waitKey(30)
        if key == 27:  # ESC键退出
            break
        elif key == ord('m') or key == ord('M'):  # 按M键切换模式
            USE_THRESHOLD_FILE = not USE_THRESHOLD_FILE
            mode_text = "使用阈值文件" if USE_THRESHOLD_FILE else "使用默认参数"
            param_text = f"HSV: H({H_MIN}-{H_MAX}) S({S_MIN}-{S_MAX}) V({V_MIN}-{V_MAX}) Exp:{EXPOSURE} {dilate_text}"
            print(f"切换模式: {mode_text}")
    
    # 释放资源
    cap.release()
    cv2.destroyAllWindows()

if __name__ == "__main__":
    main() 