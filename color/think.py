import cv2

# 打开摄像头
cap = cv2.VideoCapture(0)

if not cap.isOpened():
    print("无法打开摄像头，请检查设备连接。")
else:
    # 定义最小轮廓面积
    min_contour_area = 1000
    max_contour_area = 30000

    while True:
        # 读取一帧图像
        ret, frame = cap.read()

        if not ret:
            print("无法读取帧，请检查摄像头。")
            break

        # 定义缩放比例
        scale_percent = 50
        width = int(frame.shape[1] * scale_percent / 100)
        height = int(frame.shape[0] * scale_percent / 100)
        dim = (width, height)

        # 缩放图像
        resized_image = cv2.resize(frame, dim, interpolation=cv2.INTER_AREA)
        # 转换为灰度图像
        gray_image = cv2.cvtColor(resized_image, cv2.COLOR_BGR2GRAY)
        # 进行二值化处理
        _, binary_image = cv2.threshold(gray_image, 46, 255, cv2.THRESH_BINARY)

        # 查找轮廓
        contours, _ = cv2.findContours(binary_image, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)

        # 指定要显示的边数
        target_sides = 4
        approximated_contours = []
        for contour in contours:
            # 计算轮廓面积
            area = cv2.contourArea(contour)
            if area > min_contour_area and area < max_contour_area:
                # 计算轮廓周长
                print("轮廓面积:", area)
                # 进行多边形逼近
                epsilon = 0.03 * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)
                if len(approx) == target_sides:
                    approximated_contours.append((approx, area))

        # 按面积排序，面积大的为外框，面积小的为内框
        approximated_contours.sort(key=lambda x: x[1], reverse=True)

        # 在原始缩放图像上绘制指定边数的轮廓
        contour_image = resized_image.copy()
        
        # 如果检测到至少两个矩形（外框和内框）
        if len(approximated_contours) >= 2:
            outer_rect = approximated_contours[0][0]  # 外框
            inner_rect = approximated_contours[1][0]  # 内框
            
            # 对顶点进行排序，确保对应关系正确
            def sort_vertices(vertices):
                # 计算质心
                center_x = sum([p[0][0] for p in vertices]) / 4
                center_y = sum([p[0][1] for p in vertices]) / 4
                
                # 按角度排序（从左上角开始，顺时针）
                import math
                def angle_from_center(point):
                    return math.atan2(point[0][1] - center_y, point[0][0] - center_x)
                
                return sorted(vertices, key=angle_from_center)
            
            # 排序顶点
            outer_sorted = sort_vertices(outer_rect)
            inner_sorted = sort_vertices(inner_rect)
            
            # 计算对应顶点的中点
            for i in range(4):
                outer_x, outer_y = outer_sorted[i][0]
                inner_x, inner_y = inner_sorted[i][0]
                
                # 计
                # 算中点坐标
                mid_x = (outer_x + inner_x) // 2
                mid_y = (outer_y + inner_y) // 2
                
                # 绘制中点十字标记
                cross_size = 10
                cv2.line(contour_image, (mid_x-cross_size, mid_y), (mid_x+cross_size, mid_y), (255, 255, 0), 2)
                cv2.line(contour_image, (mid_x, mid_y-cross_size), (mid_x, mid_y+cross_size), (255, 255, 0), 2)
                
                # 标注中点坐标
                cv2.putText(contour_image, f"({mid_x},{mid_y})", (mid_x+15, mid_y-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
                
                print(f"中点坐标: ({mid_x}, {mid_y})")
        
        else:
            # 如果只有一个矩形，正常绘制
            for i, (approx, area) in enumerate(approximated_contours):
                color = (0, 0, 255) if i == 0 else (0, 255, 0)
                cv2.drawContours(contour_image, [approx], -1, color, 2)


        # 显示原始图像
        cv2.imshow('Original Image', resized_image)
        # 显示灰度图像
        #cv2.imshow('Grayscale Image', gray_image)
        # 显示二值化图像
        #cv2.imshow('Binary Image', binary_image)
        # 显示带有指定边数轮廓的图像
        cv2.imshow('Contour Image with Specified Sides', contour_image)

        # 按 'Esc' 键退出循环
        if cv2.waitKey(1) == 27:
            break

    # 释放摄像头并关闭所有窗口
    cap.release()
    cv2.destroyAllWindows()
    
