import cv2
import numpy as np

class ROIObjectDetector:
    def __init__(self):
        # 黑色阈值参数 (HSV色彩空间更稳定)
        self.black_lower = np.array([0, 0, 0])
        self.black_upper = np.array([180, 255, 60])

        # 形态学操作核
        self.kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))

        # ROI矩形检测参数
        self.min_rect_area = 1000      # 最小矩形面积
        self.max_rect_area = 50000     # 最大矩形面积
        self.rect_aspect_ratio_min = 0.3  # 矩形最小宽高比
        self.rect_aspect_ratio_max = 3.0  # 矩形最大宽高比

        # 工作流程状态
        self.workflow_state = "detecting_roi"  # detecting_roi, detecting_objects
        self.selected_roi = None

    def detect_rectangles(self, img):
        """检测矩形ROI区域 - 改进版本，参考shape_unclose.py"""
        # 图像预处理 - 使用更强的预处理
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # 高斯模糊去噪
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)

        # 边缘检测 - 调整参数以更好地检测矩形
        edges = cv2.Canny(blurred, 50, 150)

        # 形态学操作 - 连接断开的边缘
        kernel = np.ones((3, 3), np.uint8)
        edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)

        # 查找轮廓
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        rectangles = []

        print(f"检测到 {len(contours)} 个轮廓")  # 调试信息

        for i, contour in enumerate(contours):
            # 计算轮廓面积
            area = cv2.contourArea(contour)
            print(f"轮廓 {i}: 面积 = {area}")  # 调试信息

            # 面积过滤
            if area < self.min_rect_area or area > self.max_rect_area:
                continue

            # 计算轮廓周长
            perimeter = cv2.arcLength(contour, True)

            # 多边形逼近 - 使用更精确的逼近
            epsilon = 0.02 * perimeter
            approx = cv2.approxPolyDP(contour, epsilon, True)

            print(f"轮廓 {i}: 顶点数 = {len(approx)}")  # 调试信息

            # 检查是否为四边形（矩形）
            if len(approx) == 4:
                # 计算边界矩形
                x, y, w, h = cv2.boundingRect(approx)
                aspect_ratio = float(w) / h

                print(f"矩形候选: 位置({x},{y}), 尺寸({w}x{h}), 宽高比={aspect_ratio:.2f}")  # 调试信息

                # 宽高比检查
                if self.rect_aspect_ratio_min <= aspect_ratio <= self.rect_aspect_ratio_max:
                    # 计算轮廓的紧密度（面积与边界矩形面积的比值）
                    rect_area = w * h
                    compactness = area / rect_area if rect_area > 0 else 0

                    # 只接受相对紧密的矩形（避免奇怪形状）
                    if compactness > 0.7:  # 至少70%的填充度
                        rectangles.append({
                            'contour': contour,
                            'approx': approx,
                            'bbox': (x, y, w, h),
                            'area': area,
                            'aspect_ratio': aspect_ratio,
                            'compactness': compactness
                        })
                        print(f"✓ 矩形已添加: 面积={area:.0f}, 紧密度={compactness:.2f}")

        # 按面积排序，返回最大的矩形作为主ROI
        rectangles.sort(key=lambda x: x['area'], reverse=True)
        print(f"最终检测到 {len(rectangles)} 个有效矩形")
        return rectangles

    def detect_black_blocks_in_roi(self, img, roi_bbox):
        """在ROI区域内检测黑色块并返回轮廓 - 使用灰度化+二值化优化"""
        x, y, w, h = roi_bbox

        # 提取ROI区域
        roi_img = img[y:y+h, x:x+w]

        # 转换为灰度图像
        gray = cv2.cvtColor(roi_img, cv2.COLOR_BGR2GRAY)

        # 使用OTSU自适应二值化 - 更高效且适应性更强
        # OTSU会自动找到最佳阈值，将图像分为白色背景和黑色线条/物体
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        # 形态学操作去噪 - 保持原有的去噪逻辑
        binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, self.kernel)
        binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, self.kernel)

        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 过滤面积太小的轮廓并转换为全局坐标
        valid_contours = []
        for contour in contours:
            if cv2.contourArea(contour) > 200:  # 保持原有的面积阈值
                # 转换为全局坐标
                contour_global = contour + np.array([x, y])
                valid_contours.append(contour_global)

        return valid_contours, binary

    def detect_black_blocks(self, img):
        """原始的全图检测黑色块函数（保持兼容性）"""
        # 转换到HSV色彩空间
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)

        # 创建黑色掩码
        mask = cv2.inRange(hsv, self.black_lower, self.black_upper)

        # 形态学操作去噪
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, self.kernel)
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, self.kernel)

        # 查找轮廓
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 过滤面积太小的轮廓
        valid_contours = [c for c in contours if cv2.contourArea(c) > 500]

        return valid_contours, mask
    
    def extract_center_points(self, contours):
        """提取轮廓中心点"""
        centers = []
        for contour in contours:
            # 计算轮廓的矩
            M = cv2.moments(contour)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                centers.append((cx, cy))
        return centers

    def process_frame(self, img):
        """处理流程：先检测ROI，再检测黑色物体"""
        if self.workflow_state == "detecting_roi":
            # 步骤1: 检测矩形ROI
            rectangles = self.detect_rectangles(img)

            if len(rectangles) > 0:
                # 自动选择最大的矩形作为ROI
                self.selected_roi = rectangles[0]
                self.workflow_state = "detecting_objects"
                print(f"自动选择ROI: {self.selected_roi['bbox']}, 面积: {self.selected_roi['area']:.0f}")
                return "roi_detected", rectangles, None
            else:
                return "no_roi", [], None

        elif self.workflow_state == "detecting_objects":
            # 步骤2: 在ROI内检测黑色物体
            if self.selected_roi is None:
                self.workflow_state = "detecting_roi"
                return "roi_lost", [], None

            # 在ROI内检测黑色块
            contours, mask = self.detect_black_blocks_in_roi(img, self.selected_roi['bbox'])

            # 提取中心点
            centers = self.extract_center_points(contours)

            return "objects_detected", [self.selected_roi], {
                'centers': centers,
                'contours': contours,
                'mask': mask,
                'count': len(centers)
            }
    
    def visualize_results(self, img, status, rectangles):
        """可视化检测结果"""
        result_img = img.copy()

        # 绘制ROI矩形
        if rectangles:
            for i, rect in enumerate(rectangles):
                if 'bbox' in rect:
                    x, y, w, h = rect['bbox']
                    color = (0, 255, 0) if self.selected_roi == rect else (255, 255, 0)
                    # 绘制ROI矩形框（加粗显示）
                    cv2.rectangle(result_img, (x, y), (x+w, y+h), color, 3)
                    cv2.putText(result_img, f"ROI {i}", (x, y-10),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
                    cv2.putText(result_img, f"Area: {rect['area']:.0f}", (x, y+h+25),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

        # 显示状态信息
        status_text = {
            "detecting_roi": "Step 1: Detecting Rectangle ROI",
            "roi_detected": "Step 1: ROI Detected",
            "no_roi": "Step 1: No ROI Found",
            "objects_detected": "Step 2: Black Objects Detected",
            "roi_lost": "ROI Lost - Redetecting"
        }

        cv2.putText(result_img, status_text.get(status, status), (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

        return result_img

# 使用示例
def main():
    detector = ROIObjectDetector()
    cap = cv2.VideoCapture(0)
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)

    print("=== ROI黑色物体检测系统 ===")
    print("工作流程:")
    print("1. 自动检测矩形ROI区域")
    print("2. 在ROI内检测黑色物体")
    print("按 'q' 键退出程序")
    print("=" * 40)

    frame_count = 0

    while True:
        ret, frame = cap.read()
        if not ret:
            break

        frame_count += 1

        # 处理帧
        status, rectangles, detection_data = detector.process_frame(frame)

        # 可视化
        result = detector.visualize_results(frame, status, rectangles)

        # 显示结果
        cv2.imshow('Original', frame)
        cv2.imshow('ROI Object Detection', result)

        # 显示掩码（如果有检测数据）
        if isinstance(detection_data, dict) and 'mask' in detection_data:
            cv2.imshow('Mask', detection_data['mask'])

        # 打印检测信息
        if status == "objects_detected" and isinstance(detection_data, dict):
            centers = detection_data.get('centers', [])
            object_count = detection_data.get('count', 0)
            if centers:
                print(f"Frame {frame_count}: 检测到 {object_count} 个黑色物体")
                for i, center in enumerate(centers):
                    print(f"  物体 {i}: 坐标 ({center[0]}, {center[1]})")
        elif status == "roi_detected":
            print(f"Frame {frame_count}: ROI检测成功")
        elif status == "no_roi":
            if frame_count % 30 == 0:  # 每30帧打印一次，避免刷屏
                print(f"Frame {frame_count}: 未检测到ROI")

        if cv2.waitKey(1) & 0xFF == ord('q'):
            break

    cap.release()
    cv2.destroyAllWindows()

if __name__ == "__main__":
    main()